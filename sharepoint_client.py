import os

# import msal  <- No longer needed here
import requests
from typing import Optional, List, Dict, Any, BinaryIO
from pathlib import Path
import logging
import aiohttp
import json
from urllib.parse import quote, unquote

# from datetime import datetime, timedelta <- No longer needed here
from datetime import datetime, timedelta  # Added back for expiration time

logger = logging.getLogger(__name__)


class SharePointClient:
    """Client for interacting with SharePoint through Microsoft Graph API using externally provided tokens."""

    # Add __init__ method to initialize site_name from config
    def __init__(self):
        """Initialize the SharePoint client with site and library names from environment variables."""
        self.site_name = os.getenv("SHAREPOINT_SITE_NAME")
        self.library_name = os.getenv("SHAREPOINT_LIBRARY_NAME")
        self.target_folder = "DDB Group Repository"  # Located at IT Storage root level
        # Use the correct drive ID from SharePoint UI
        self.target_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
        self.site_id_cache: Dict[str, str] = {}  # Class level cache for site IDs
        logger.info(
            f"SharePointClient initialized with site_name: '{self.site_name}', library_name: '{self.library_name}'"
        )

    # Remove __init__ related to MSAL app setup
    # def __init__(self, client_id: str, client_secret: str, tenant_id: str):
    #     ... (removed MSAL setup) ...

    # Remove internal token acquisition
    # async def get_access_token(self) -> str:
    #     ... (removed) ...

    async def get_site_id(self, site_url: str, token: str) -> str:
        """Get the site ID for a SharePoint site URL using the provided token."""
        # Use cache if available
        if site_url in self.site_id_cache:
            logger.info(
                f"Using cached site ID for {site_url}: {self.site_id_cache[site_url]}"
            )
            return self.site_id_cache[site_url]

        # Log the site URL for debugging
        logger.info(f"Getting site ID for URL: '{site_url}'")

        # Check if this is a OneDrive URL (contains '/personal/')
        is_onedrive = "/personal/" in site_url.lower()
        logger.info(f"Is OneDrive personal site: {is_onedrive}")

        try:
            # Remove 'https://' if present and split into hostname and path
            site_url_formatted = site_url.replace("https://", "").strip("/")
            hostname = site_url_formatted.split("/")[0]

            if is_onedrive:
                # For OneDrive personal sites, use a different approach
                async with aiohttp.ClientSession() as session:
                    # First try to get the site directly
                    site_api_url = f"https://graph.microsoft.com/v1.0/sites/{hostname}:/personal/itstorage_ddbgroup_com_ph"
                    logger.info(f"Trying direct site lookup: {site_api_url}")

                    async with session.get(
                        site_api_url,
                        headers={"Authorization": f"Bearer {token}"},
                    ) as response:
                        if response.status == 200:
                            site_data = await response.json()
                            site_id = site_data["id"]
                            self.site_id_cache[site_url] = site_id
                            return site_id
                        else:
                            error_text = await response.text()
                            logger.warning(
                                f"Site lookup failed: {response.status} - {error_text}"
                            )

                    # If direct lookup fails, try getting the OneDrive site
                    me_site_url = "https://graph.microsoft.com/v1.0/me/drive/root"
                    logger.info(f"Trying OneDrive root: {me_site_url}")

                    async with session.get(
                        me_site_url,
                        headers={"Authorization": f"Bearer {token}"},
                    ) as response:
                        if response.status == 200:
                            drive_data = await response.json()
                            # Extract site ID from the webUrl
                            web_url = drive_data.get("webUrl", "")
                            if "id=" in web_url:
                                folder_id = web_url.split("id=")[1].split("&")[0]
                                self.site_id_cache[site_url] = folder_id
                                return folder_id

            # Fallback to hostname-only lookup
            logger.info(
                f"Trying hostname-only lookup: https://graph.microsoft.com/v1.0/sites/{hostname}"
            )
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://graph.microsoft.com/v1.0/sites/{hostname}",
                    headers={"Authorization": f"Bearer {token}"},
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(
                            f"Hostname lookup failed: {response.status} - {error_text}"
                        )
                        raise Exception(f"Could not find site ID for '{site_url}'")

                    site_data = await response.json()
                    site_id = site_data["id"]
                    self.site_id_cache[site_url] = site_id
                    return site_id

        except Exception as e:
            logger.error(f"Error getting site ID: {e}")
            raise

    async def list_sites(self, token: str) -> List[Dict]:
        """List all available SharePoint sites for the user."""
        try:
            logger.info("Starting to list SharePoint sites...")
            result = []

            # Always try to get OneDrive first
            onedrive_site = None  # Initialize to None
            try:
                logger.info("Getting OneDrive details...")
                onedrive_site = await self._get_onedrive_as_site(token)
                logger.info("Successfully retrieved OneDrive details")
                result.append(onedrive_site)
            except aiohttp.ClientResponseError as onedrive_exc:  # Catch specific error
                if onedrive_exc.status == 401:
                    logger.error(
                        f"OneDrive access failed due to expired token: {onedrive_exc}"
                    )
                    raise onedrive_exc  # Re-raise the 401 error
                else:
                    # Log other OneDrive errors but continue
                    logger.error(
                        f"Error getting OneDrive details (non-401): {onedrive_exc}",
                        exc_info=True,
                    )
            except Exception as onedrive_exc:  # Catch other general exceptions
                logger.error(
                    f"Unexpected error getting OneDrive details: {onedrive_exc}",
                    exc_info=True,
                )

            # If we have a configured site name, try to get it specifically
            configured_site_data = None  # Initialize to None
            if self.site_name:
                try:
                    logger.info(f"Getting configured site: {self.site_name}")
                    site_id = await self.get_site_id(self.site_name, token)
                    if site_id:
                        # Get the site details
                        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}"
                        async with aiohttp.ClientSession() as session:
                            async with session.get(
                                url,
                                headers={"Authorization": f"Bearer {token}"},
                            ) as response:
                                if response.status == 200:
                                    configured_site_data = (
                                        await response.json()
                                    )  # Store data
                                    logger.info(
                                        f"Found configured site: {configured_site_data.get('displayName')}"
                                    )
                                elif response.status == 401:  # Check for 401 here too
                                    error_text = await response.text()
                                    logger.error(
                                        f"Configured site access failed (401): {response.status} - {error_text}"
                                    )
                                    # Raise a ClientResponseError similar to how aiohttp would
                                    raise aiohttp.ClientResponseError(
                                        response.request_info,
                                        response.history,
                                        status=response.status,
                                        message=f"Failed to get configured site details: {error_text}",
                                        headers=response.headers,
                                    )
                                else:
                                    error_text = await response.text()
                                    logger.error(
                                        f"Failed to get configured site details (non-401): {response.status} - {error_text}"
                                    )
                except aiohttp.ClientResponseError as site_exc:  # Catch specific error
                    if site_exc.status == 401:
                        logger.error(
                            f"Configured site access failed due to expired token: {site_exc}"
                        )
                        raise site_exc  # Re-raise the 401 error
                    else:
                        # Log other site errors but potentially continue if OneDrive worked
                        logger.error(
                            f"Error getting configured site (non-401): {site_exc}",
                            exc_info=True,
                        )
                except Exception as site_exc:  # Catch other general exceptions
                    logger.error(
                        f"Unexpected error getting configured site: {site_exc}",
                        exc_info=True,
                    )

            # Add configured site to results if found and not already present
            if configured_site_data and not any(
                s.get("id") == configured_site_data.get("id") for s in result
            ):
                result.append(configured_site_data)

            # Format and return results
            formatted_sites = []
            for site in result:
                try:
                    # Determine if this is a OneDrive site
                    is_onedrive = (
                        "drive" in site.get("id", "").lower()
                        or "/personal/" in site.get("webUrl", "").lower()
                        or site.get("driveType", "").lower() == "business"
                    )

                    formatted_site = {
                        "id": site.get("id", ""),
                        "displayName": site.get("displayName", "Unknown"),
                        "webUrl": site.get("webUrl", ""),
                        "siteType": "OneDrive" if is_onedrive else "SharePoint",
                    }
                    formatted_sites.append(formatted_site)
                except Exception as format_exc:
                    logger.error(f"Error formatting site data: {format_exc}")

            logger.info(f"Returning {len(formatted_sites)} total sites")
            return formatted_sites

        except Exception as e:
            logger.error(f"Error in list_sites: {e}", exc_info=True)
            if isinstance(e, aiohttp.ClientResponseError):
                status = getattr(e, "status", None)
                message = getattr(e, "message", str(e))

                if status == 401:
                    raise Exception(
                        "Authentication failed. Token may have expired. Please log in again."
                    )
                elif status == 403:
                    raise Exception(
                        "Permission denied. You may not have access to SharePoint sites."
                    )
                elif status == 404:
                    site_info = f" ('{self.site_name}')" if self.site_name else ""
                    raise Exception(
                        f"Site not found{site_info}. Check your SHAREPOINT_SITE_NAME in .env."
                    )
                else:
                    raise Exception(f"SharePoint API error: {status} - {message}")
            raise Exception(f"Failed to list SharePoint sites: {str(e)}")

    async def _get_site_details(self, token: str) -> Dict:
        """Get details for the configured SharePoint site."""
        if not self.site_name or self.site_name.strip() == "":
            logger.error("No SharePoint site name configured")
            raise ValueError("SharePoint site name is not configured or is empty.")

        try:
            logger.info(f"Getting site ID for site name '{self.site_name}'")
            site_id = await self._get_site_id(token)
            logger.info(f"Retrieved site ID: {site_id}")

            url = f"https://graph.microsoft.com/v1.0/sites/{site_id}"
            headers = {
                "Authorization": f"Bearer {token}",
                "Accept": "application/json",
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        site_data = await response.json()
                        logger.info(
                            f"Successfully got site details for '{self.site_name}'"
                        )
                        return site_data
                    else:
                        error_text = await response.text()
                        logger.error(
                            f"Failed to get site details: {response.status} - {error_text}"
                        )
                        raise aiohttp.ClientResponseError(
                            status=response.status,
                            message=f"Failed to get site details: {error_text}",
                            headers=response.headers,
                            request_info=response.request_info,
                            history=response.history,
                        )
        except Exception as e:
            logger.error(f"Error in _get_site_details: {e}", exc_info=True)
            if isinstance(e, aiohttp.ClientResponseError):
                logger.error(
                    f"Status: {getattr(e, 'status', 'unknown')}, Message: {getattr(e, 'message', str(e))}"
                )
            raise e

    async def _get_site_id(self, token: str) -> str:
        """Get the site ID for the configured SharePoint site."""
        if not self.site_name or self.site_name.strip() == "":
            logger.error("No SharePoint site name configured")
            raise ValueError("SharePoint site name is not configured or is empty.")

        try:
            logger.info(f"Getting site ID for site '{self.site_name}'")

            # First, try the direct way with host and site path
            if "/" in self.site_name:
                parts = self.site_name.split("/", 1)
                if len(parts) == 2:
                    host, site_path = parts
                    logger.info(
                        f"Parsed site name into host: '{host}' and path: '{site_path}'"
                    )

                    url = f"https://graph.microsoft.com/v1.0/sites/{host}:/sites/{site_path}"
                    headers = {
                        "Authorization": f"Bearer {token}",
                        "Accept": "application/json",
                    }

                    async with aiohttp.ClientSession() as session:
                        async with session.get(url, headers=headers) as response:
                            if response.status == 200:
                                data = await response.json()
                                site_id = data.get("id")
                                logger.info(
                                    f"Successfully got site ID with host/path approach: {site_id}"
                                )
                                return site_id
                            else:
                                error_text = await response.text()
                                logger.warning(
                                    f"Failed to get site ID with host/path approach: {response.status} - {error_text}"
                                )
                                # We'll fall back to the search approach

            # Try the search approach as a fallback
            logger.info(f"Trying search approach to find site '{self.site_name}'")
            url = f"https://graph.microsoft.com/v1.0/sites?search={self.site_name}"
            headers = {
                "Authorization": f"Bearer {token}",
                "Accept": "application/json",
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        sites = data.get("value", [])

                        if not sites:
                            logger.error(f"No sites found matching '{self.site_name}'")
                            raise ValueError(
                                f"Site '{self.site_name}' not found in the tenant."
                            )

                        # Try to find an exact match first
                        for site in sites:
                            display_name = site.get("displayName", "")
                            web_url = site.get("webUrl", "")

                            # Check if site name matches displayName or is in the URL
                            if (
                                display_name
                                and self.site_name.lower() == display_name.lower()
                            ) or (
                                web_url and self.site_name.lower() in web_url.lower()
                            ):
                                site_id = site.get("id")
                                logger.info(f"Found matching site ID: {site_id}")
                                return site_id

                        # If no exact match, take the first result
                        if sites:
                            site_id = sites[0].get("id")
                            logger.warning(
                                f"No exact match found. Using first result with ID: {site_id}"
                            )
                            return site_id
                    else:
                        error_text = await response.text()
                        logger.error(
                            f"Failed to search for site: {response.status} - {error_text}"
                        )
                        raise aiohttp.ClientResponseError(
                            status=response.status,
                            message=f"Failed to search for site: {error_text}",
                            headers=response.headers,
                            request_info=response.request_info,
                            history=response.history,
                        )

            raise ValueError(f"Could not find site ID for '{self.site_name}'")

        except Exception as e:
            logger.error(f"Error in _get_site_id: {e}", exc_info=True)
            if isinstance(e, aiohttp.ClientResponseError):
                logger.error(
                    f"Status: {getattr(e, 'status', 'unknown')}, Message: {getattr(e, 'message', str(e))}"
                )
            raise Exception(f"Failed to get site ID for '{self.site_name}': {e}")

    async def _get_onedrive_drive_id_by_upn(
        self, user_principal_name: str, token: str
    ) -> Optional[str]:
        """Get the Drive ID for a user's OneDrive using their UPN (Requires User.Read.All App Permission)."""
        if not user_principal_name:
            logger.error("User Principal Name is required to get OneDrive drive ID.")
            return None

        url = f"https://graph.microsoft.com/v1.0/users/{user_principal_name}/drive"
        headers = {"Authorization": f"Bearer {token}"}
        logger.info(
            f"Attempting to get OneDrive drive ID for UPN: {user_principal_name} via URL: {url}"
        )

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        drive_data = await response.json()
                        drive_id = drive_data.get("id")
                        if drive_id:
                            logger.info(
                                f"Successfully found OneDrive drive ID: {drive_id} for UPN: {user_principal_name}"
                            )
                            return drive_id
                        else:
                            logger.error(
                                f"OneDrive drive data retrieved for {user_principal_name}, but ID is missing."
                            )
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(
                            f"Failed to get OneDrive drive for {user_principal_name}: {response.status} - {error_text}"
                        )
                        # Raise specific exceptions based on status? (e.g., 404 user not found, 403 permissions)
                        raise Exception(
                            f"API error getting drive for {user_principal_name}: {response.status} - {error_text}"
                        )
        except Exception as e:
            logger.error(
                f"Error getting OneDrive drive ID for {user_principal_name}: {e}",
                exc_info=True,
            )
            raise  # Re-raise the exception to be caught by the caller

    async def _get_onedrive_as_site(self, token: str) -> Dict:
        """Get user's OneDrive as a site-like object."""
        try:
            logger.info("Getting OneDrive as a site")
            url = "https://graph.microsoft.com/v1.0/me/drive"
            headers = {
                "Authorization": f"Bearer {token}",
                "Accept": "application/json",
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        drive_data = await response.json()
                        drive_id = drive_data.get("id")
                        web_url = drive_data.get("webUrl", "")

                        # Get user info for better display name
                        me_url = "https://graph.microsoft.com/v1.0/me"
                        async with session.get(me_url, headers=headers) as me_response:
                            if me_response.status == 200:
                                me_data = await me_response.json()
                                display_name = (
                                    f"{me_data.get('displayName', 'My')} OneDrive"
                                )
                            else:
                                display_name = "My OneDrive"

                        # Create a site-like structure
                        onedrive_site = {
                            "id": drive_id,  # Use the drive ID directly
                            "displayName": display_name,
                            "webUrl": web_url,
                            "driveType": "business",  # Always business for OneDrive for Business
                        }
                        logger.info("Successfully created OneDrive site representation")
                        return onedrive_site
                    else:
                        error_text = await response.text()
                        logger.error(
                            f"Failed to get OneDrive: {response.status} - {error_text}"
                        )
                        raise aiohttp.ClientResponseError(
                            status=response.status,
                            message=f"Failed to get OneDrive: {error_text}",
                            headers=response.headers,
                            request_info=response.request_info,
                            history=response.history,
                        )
        except Exception as e:
            logger.error(f"Error in _get_onedrive_as_site: {e}", exc_info=True)
            if isinstance(e, aiohttp.ClientResponseError):
                logger.error(
                    f"Status: {getattr(e, 'status', 'unknown')}, Message: {getattr(e, 'message', str(e))}"
                )
            raise Exception(f"Failed to get OneDrive as site: {e}")

    async def list_drives(self, site_id: str, token: str) -> List[Dict[str, Any]]:
        """List all document libraries (drives) in a SharePoint site using the provided token."""
        try:
            # For OneDrive personal sites, we need to use a different endpoint
            if "!" in site_id and site_id.startswith("b!"):
                # For OneDrive, we should use /me/drive
                logger.info(f"Getting OneDrive drive details using /me/drive")
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        "https://graph.microsoft.com/v1.0/me/drive",
                        headers={"Authorization": f"Bearer {token}"},
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(
                                f"Failed to get OneDrive: {response.status} - {error_text}"
                            )
                            raise Exception(f"Failed to get OneDrive: {error_text}")

                        drive_data = await response.json()
                        logger.info(f"Successfully retrieved OneDrive details")
                        return [drive_data]  # Return as a list with single drive
            else:
                # For regular SharePoint sites, use the original endpoint
                logger.info(f"Getting drives for SharePoint site: {site_id}")
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives",
                        headers={"Authorization": f"Bearer {token}"},
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(
                                f"Failed to list drives: {response.status} - {error_text}"
                            )
                            raise Exception(f"Failed to list drives: {error_text}")
                        data = await response.json()
                        return data.get("value", [])
        except Exception as e:
            logger.error(f"Error in list_drives: {str(e)}")
            raise

    async def list_all_drives(self, token: str) -> List[Dict[str, Any]]:
        """List all drives across all available sites for the user."""
        all_drives = []
        try:
            logger.info("Getting all drives across all sites...")
            
            # Get all sites first
            sites = await self.list_sites(token)
            logger.info(f"Found {len(sites)} sites to check for drives")
            
            for site in sites:
                site_id = site.get("id")
                site_name = site.get("displayName", "Unknown")
                site_type = site.get("siteType", "Unknown")
                site_url = site.get("webUrl", "Unknown")
                
                logger.info(f"Checking drives for site: {site_name} ({site_type}) - ID: {site_id}")
                
                try:
                    # Get drives for this site
                    drives = await self.list_drives(site_id, token)
                    logger.info(f"Found {len(drives)} drives for site {site_name}")
                    
                    # Add site context to each drive
                    for drive in drives:
                        drive_info = {
                            "drive_id": drive.get("id"),
                            "drive_name": drive.get("name"),
                            "drive_type": drive.get("driveType"),
                            "drive_web_url": drive.get("webUrl"),
                            "site_id": site_id,
                            "site_name": site_name,
                            "site_type": site_type,
                            "site_url": site_url
                        }
                        all_drives.append(drive_info)
                        logger.info(f"  Drive: {drive.get('name')} (ID: {drive.get('id')}) - Type: {drive.get('driveType')}")
                        
                except Exception as drive_error:
                    logger.error(f"Failed to get drives for site {site_name}: {drive_error}")
                    continue
            
            logger.info(f"Total drives found across all sites: {len(all_drives)}")
            return all_drives
            
        except Exception as e:
            logger.error(f"Error in list_all_drives: {str(e)}")
            raise

    async def check_folder_exists_in_drive(self, drive_id: str, folder_name: str, token: str) -> Dict[str, Any]:
        """Check if a specific folder exists in a drive's root and return its details."""
        try:
            logger.info(f"Checking for folder '{folder_name}' in drive {drive_id}")
            
            # Get root folder contents
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children",
                    headers={"Authorization": f"Bearer {token}"},
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Failed to list root contents for drive {drive_id}: {response.status} - {error_text}")
                        return {"found": False, "error": f"Failed to access drive: {error_text}"}
                    
                    data = await response.json()
                    items = data.get("value", [])
                    
                    # Look for the specific folder
                    for item in items:
                        if item.get("name") == folder_name and "folder" in item:
                            logger.info(f"Found folder '{folder_name}' in drive {drive_id}")
                            return {
                                "found": True,
                                "folder_id": item.get("id"),
                                "folder_name": item.get("name"),
                                "folder_path": f"/{folder_name}",
                                "created_date": item.get("createdDateTime"),
                                "modified_date": item.get("lastModifiedDateTime"),
                                "item_count": item.get("folder", {}).get("childCount", 0)
                            }
                    
                    logger.info(f"Folder '{folder_name}' not found in drive {drive_id}")
                    return {"found": False}
                    
        except Exception as e:
            logger.error(f"Error checking for folder '{folder_name}' in drive {drive_id}: {str(e)}")
            return {"found": False, "error": str(e)}

    async def list_files(
        self, drive_id: str, token: str, folder_path: str = ""
    ) -> List[dict]:
        """List files and folders in a SharePoint drive or folder."""
        try:
            # If no specific folder_path is provided, use the target folder
            if not folder_path and hasattr(self, "target_folder"):
                folder_path = self.target_folder

            # Construct the API URL
            if folder_path:
                # URL encode the folder path and handle special characters
                safe_path = folder_path.replace("'", "''")  # Handle single quotes
                encoded_path = quote(safe_path)
                api_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{encoded_path}:/children"
                logger.info(f"Listing files from folder: {folder_path}")
            else:
                api_url = (
                    f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
                )
                logger.info("Listing files from root folder")

            logger.info(f"Using API URL: {api_url}")

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    api_url,
                    headers={"Authorization": f"Bearer {token}"},
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(
                            f"Error listing files: {response.status} - {error_text}"
                        )

                        # Check if folder not found
                        if response.status == 404:
                            # Try listing root folder instead
                            logger.info("Folder not found, trying root folder")
                            root_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
                            async with session.get(
                                root_url,
                                headers={"Authorization": f"Bearer {token}"},
                            ) as root_response:
                                if root_response.status == 200:
                                    data = await root_response.json()
                                    files = data.get("value", [])
                                else:
                                    root_error = await root_response.text()
                                    raise Exception(
                                        f"Failed to list files: {root_error}"
                                    )
                        else:
                            raise Exception(f"Failed to list files: {error_text}")
                    else:
                        data = await response.json()
                        files = data.get("value", [])

                    # Process the files
                    processed_files = []
                    for file in files:
                        processed_file = {
                            "name": file.get("name", ""),
                            "id": file.get("id", ""),
                            "web_url": file.get("webUrl", ""),
                            "size": file.get("size", 0),
                            "created_datetime": file.get("createdDateTime", ""),
                            "last_modified_datetime": file.get(
                                "lastModifiedDateTime", ""
                            ),
                            "is_folder": "folder" in file,
                        }
                        processed_files.append(processed_file)

                    logger.info(f"Found {len(processed_files)} files/folders")
                    return processed_files

        except Exception as e:
            logger.error(f"Error listing files: {e}")
            raise

    async def list_folder_contents(
        self, drive_id: str, token: str, folder_id: str = None
    ) -> List[Dict[str, Any]]:
        """List the contents of a folder in a SharePoint drive.
        
        Args:
            drive_id (str): The ID of the drive
            token (str): The access token for authentication
            folder_id (str, optional): The ID of the folder to list. If None, lists root folder contents.
            
        Returns:
            List[Dict[str, Any]]: List of items with their names, IDs, types, and sizes
        """
        try:
            # Construct the API URL
            if folder_id:
                api_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{folder_id}/children"
                logger.info(f"Listing contents of folder ID: {folder_id}")
            else:
                api_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
                logger.info("Listing contents of root folder")
                
            logger.info(f"Using API URL: {api_url}")

            headers = {"Authorization": f"Bearer {token}"}

            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, headers=headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(
                            f"Error listing folder contents: {response.status} - {error_text}"
                        )
                        
                        if response.status == 404:
                            raise Exception(
                                f"Folder not found: {folder_id if folder_id else 'root'}"
                            )
                        elif response.status == 401:
                            raise Exception(
                                "Authentication error. Token may be invalid or expired."
                            )
                        elif response.status == 403:
                            raise Exception(
                                f"Permission denied accessing folder: {folder_id if folder_id else 'root'}"
                            )
                        else:
                            raise Exception(f"Failed to list folder contents: {error_text}")

                    data = await response.json()
                    items = data.get("value", [])

                    # Process the items to return structured information
                    processed_items = []
                    for item in items:
                        # Determine item type based on Microsoft Graph API properties
                        # Folders have a "folder" property, files have a "file" property
                        is_folder = "folder" in item
                        item_type = "folder" if is_folder else "file"
                        
                        # Debug logging to verify type detection
                        logger.debug(f"Item '{item.get('name', '')}' (ID: {item.get('id', '')}) - has 'folder' property: {is_folder}, detected type: {item_type}")
                        
                        item_info = {
                            "id": item.get("id", ""),
                            "name": item.get("name", ""),
                            "type": item_type,
                            "size": item.get("size", 0),
                            "created_datetime": item.get("createdDateTime", ""),
                            "last_modified_datetime": item.get("lastModifiedDateTime", ""),
                            "web_url": item.get("webUrl", ""),
                        }
                        
                        # Add additional folder-specific information if it's a folder
                        if "folder" in item:
                            folder_info = item.get("folder", {})
                            item_info["child_count"] = folder_info.get("childCount", 0)
                        
                        # Add file-specific information if it's a file
                        elif "file" in item:
                            file_info = item.get("file", {})
                            item_info["mime_type"] = file_info.get("mimeType", "")
                            
                        processed_items.append(item_info)

                    logger.info(f"Found {len(processed_items)} items in folder")
                    return processed_items

        except Exception as e:
            logger.error(f"Error listing folder contents: {e}", exc_info=True)
            raise Exception(f"Failed to list folder contents: {str(e)}")

    async def list_folder_contents_recursive(
        self, drive_id: str, folder_id: str, token: str
    ):
        """Recursively lists all files within a given folder ID, yielding file info."""
        logger.info(f"Recursively listing contents of folder ID: {folder_id}")
        api_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{folder_id}/children"
        headers = {"Authorization": f"Bearer {token}"}

        try:
            async with aiohttp.ClientSession() as session:
                next_page_url = api_url
                while next_page_url:
                    async with session.get(next_page_url, headers=headers) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(
                                f"Error listing children of {folder_id}: {response.status} - {error_text}"
                            )
                            # Stop processing this branch if there's an error
                            return
                        data = await response.json()
                        children = data.get("value", [])
                        logger.info(
                            f"Found {len(children)} children in {folder_id} page"
                        )

                        for item in children:
                            item_id = item.get("id")
                            item_name = item.get("name")
                            if not item_id or not item_name:
                                logger.warning(
                                    f"Skipping item with missing ID or name: {item}"
                                )
                                continue

                            if "folder" in item:
                                # It's a subfolder, recurse
                                logger.info(
                                    f"Entering subfolder: {item_name} ({item_id})"
                                )
                                async for (
                                    file_info
                                ) in self.list_folder_contents_recursive(
                                    drive_id, item_id, token
                                ):
                                    yield file_info  # Yield results from subfolder
                            elif "file" in item:
                                # It's a file, yield its info
                                logger.info(f"Yielding file: {item_name} ({item_id})")
                                yield {
                                    "id": item_id,
                                    "name": item_name,
                                    "size": item.get("size", 0),
                                    # Add other relevant fields if needed
                                }
                            else:
                                logger.warning(
                                    f"Skipping item '{item_name}' ({item_id}) as it is neither file nor folder."
                                )

                        # Check for pagination
                        next_page_url = data.get("@odata.nextLink")
                        if next_page_url:
                            logger.info(f"Found next page link for folder {folder_id}")

        except aiohttp.ClientError as ce:
            logger.error(
                f"Client error during recursive listing for {folder_id}: {ce}",
                exc_info=True,
            )
            # Optionally re-raise or handle differently
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error during recursive listing for {folder_id}: {e}",
                exc_info=True,
            )
            raise  # Re-raise unexpected errors

    async def download_file(
        self,
        drive_id: str,
        file_id: str,
        save_path: str,
        token: str,
    ) -> None:
        """Download the content of a file using the provided token and save it locally."""
        try:
            # --- Get metadata first to check item type ---
            logger.info(
                f"Fetching metadata for file {file_id} before download attempt..."
            )
            metadata = await self.get_file_metadata(drive_id, file_id, token)
            logger.info(f"Metadata for {file_id}: {metadata}")  # Log the full metadata

            # Check for specific indicators like folder or package
            if metadata.get("folder"):
                logger.error(
                    f"Item {file_id} is a folder, cannot download content directly."
                )
                raise ValueError(f"Item {file_id} is a folder, not a file.")
            if metadata.get("package"):
                logger.warning(
                    f"Item {file_id} is a package (e.g., OneNote). Direct download might fail."
                )
            # --- End metadata check ---

            # Use the file ID directly to get the content URL
            download_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{file_id}/content"
            logger.info(
                f"Attempting download from content URL for file {file_id}: {download_url}"
            )
            headers = {"Authorization": f"Bearer {token}"}

            # Download the file content
            async with aiohttp.ClientSession() as session:
                async with session.get(download_url, headers=headers) as response:
                    logger.info(
                        f"Download request for {file_id} returned status: {response.status}"
                    )
                    response.raise_for_status()
                    content = await response.read()
                    logger.info(
                        f"Successfully downloaded file {file_id} ({len(content)} bytes)"
                    )

                    # Write content to the specified path
                    with open(save_path, "wb") as f:
                        f.write(content)
                    logger.info(f"Successfully saved file {file_id} to {save_path}")

                    # No return value needed as we save to file
                    return

        except aiohttp.ClientResponseError as http_err:
            # Access status and message directly from the exception object
            status = http_err.status
            message = http_err.message
            url = http_err.request_info.url
            logger.error(
                f"HTTP Error downloading file: {status} - {message} (URL: {url})",
                exc_info=True,  # Keep traceback for debugging
            )
            # Add metadata details to the exception if available
            metadata_details = (
                f" (Metadata: {metadata})" if "metadata" in locals() else ""
            )
            raise Exception(
                f"HTTP Error {status}: {message} (URL: {url}){metadata_details}"
            )
        except ValueError as ve:
            logger.error(
                f"Value Error during download preparation: {ve}", exc_info=True
            )
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error downloading file {file_id}: {e}", exc_info=True
            )
            # Add metadata details to the exception if available
            metadata_details = (
                f" (Metadata: {metadata})" if "metadata" in locals() else ""
            )
            raise Exception(
                f"Unexpected error downloading file {file_id}: {e}{metadata_details}"
            )

    async def get_file_metadata(
        self, drive_id: str, file_id: str, token: str
    ) -> Dict[str, Any]:
        """Get metadata for a file."""
        try:
            # Use the items endpoint instead of root
            url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{file_id}"
            logger.info(f"Getting metadata for file {file_id} from {url}")

            headers = {"Authorization": f"Bearer {token}"}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(
                            f"Error getting file metadata: {response.status} - {error_text}"
                        )
                        raise Exception(f"Failed to get file metadata: {error_text}")

                    metadata = await response.json()
                    logger.info(f"Successfully got metadata for file {file_id}")
                    return metadata

        except Exception as e:
            logger.error(f"Error getting file metadata: {e}")
            raise

    async def get_drive_item(
        self, drive_id: str, item_id: str, token: str
    ) -> Dict[str, Any]:
        """Get metadata for a specific item (file or folder) in a drive using the provided token."""
        if not drive_id or not item_id:
            raise ValueError("Drive ID and Item ID cannot be empty.")

        # Construct the API URL
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{item_id}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Accept": "application/json",
        }
        logger.info(f"Getting item metadata from URL: {url}")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        item_data = await response.json()
                        logger.info(
                            f"Successfully retrieved metadata for item '{item_id}' in drive '{drive_id}'"
                        )
                        return item_data
                    else:
                        error_text = await response.text()
                        logger.error(
                            f"Failed to get metadata for item '{item_id}': {response.status} - {error_text}"
                        )
                        # More specific error checking
                        if response.status == 401:
                            raise Exception(
                                f"Authentication error (401) getting item metadata. Token may be invalid or expired."
                            )
                        elif response.status == 403:
                            raise Exception(
                                f"Permission error (403) getting item metadata. Check permissions for item '{item_id}'."
                            )
                        elif response.status == 404:
                            raise Exception(
                                f"Item not found (404). Drive '{drive_id}' or item '{item_id}' may not exist."
                            )
                        else:
                            raise Exception(
                                f"API error getting item metadata: {response.status} - {error_text}"
                            )

        except Exception as e:
            logger.error(f"Error getting drive item '{item_id}': {e}", exc_info=True)
            # Re-raise with specific message
            raise Exception(f"Failed to get drive item metadata: {str(e)}")

    # -------------------------------------------------------------------------
    # Webhook Subscription Methods
    # -------------------------------------------------------------------------

    async def create_webhook_subscription(
        self,
        drive_id: str,
        notification_url: str,
        token: str,
        client_state: str = "DDBSharepointWebhookSecret",
    ) -> Dict[str, Any]:
        """Create a webhook subscription for changes in the root of the specified drive."""
        if not drive_id:
            raise ValueError("Drive ID is required to create a subscription.")
        if not notification_url:
            raise ValueError("Notification URL is required.")

        subscription_url = "https://graph.microsoft.com/v1.0/subscriptions"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }

        # Calculate expiration time (max 3 days recommended, can be up to ~423 days max by API)
        # Let's use 3 days for now
        expiration_datetime = datetime.utcnow() + timedelta(days=3)
        expiration_datetime_str = expiration_datetime.strftime("%Y-%m-%dT%H:%M:%SZ")

        payload = {
            "changeType": "updated",  # Covers create, update, delete for drive items
            "notificationUrl": notification_url,
            "resource": f"/drives/{drive_id}/root",
            "expirationDateTime": expiration_datetime_str,
            "clientState": client_state,
        }

        logger.info(
            f"Attempting to create webhook subscription for drive {drive_id} with notification URL {notification_url}"
        )
        logger.debug(
            f"Subscription payload: {json.dumps(payload)}"
        )  # Log payload for debugging

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    subscription_url, headers=headers, json=payload
                ) as response:
                    response_data = await response.json()
                    if response.status in [200, 201]:  # 201 Created is expected
                        logger.info(
                            f"Successfully created webhook subscription: {response_data.get('id')}"
                        )
                        return response_data
                    else:
                        error_text = (
                            await response.text()
                        )  # Get text if json fails or for more detail
                        logger.error(
                            f"Failed to create webhook subscription: {response.status} - {error_text}"
                        )
                        logger.error(
                            f"Response body: {response_data}"
                        )  # Log the JSON error body if available
                        raise Exception(
                            f"API error creating subscription: {response.status} - {error_text} - Body: {response_data}"
                        )
        except Exception as e:
            logger.error(f"Error creating webhook subscription: {e}", exc_info=True)
            raise Exception(f"Failed to create webhook subscription: {str(e)}")

    async def renew_webhook_subscription(
        self, subscription_id: str, token: str
    ) -> Dict[str, Any]:
        """Renew an existing webhook subscription by extending its expiration date."""
        if not subscription_id:
            raise ValueError("Subscription ID is required to renew.")

        subscription_url = (
            f"https://graph.microsoft.com/v1.0/subscriptions/{subscription_id}"
        )
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }

        # Calculate new expiration time (extend by 3 days from now)
        new_expiration_datetime = datetime.utcnow() + timedelta(days=3)
        new_expiration_datetime_str = new_expiration_datetime.strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )

        payload = {"expirationDateTime": new_expiration_datetime_str}

        logger.info(f"Attempting to renew webhook subscription {subscription_id}")
        logger.debug(f"Renewal payload: {json.dumps(payload)}")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.patch(
                    subscription_url, headers=headers, json=payload
                ) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        logger.info(
                            f"Successfully renewed webhook subscription {subscription_id}. New expiration: {response_data.get('expirationDateTime')}"
                        )
                        return response_data
                    else:
                        error_text = await response.text()
                        response_data = {}
                        try:
                            response_data = (
                                await response.json()
                            )  # Try to get JSON error body
                        except:
                            pass  # Ignore if response is not JSON
                        logger.error(
                            f"Failed to renew webhook subscription {subscription_id}: {response.status} - {error_text}"
                        )
                        logger.error(f"Response body: {response_data}")
                        raise Exception(
                            f"API error renewing subscription {subscription_id}: {response.status} - {error_text} - Body: {response_data}"
                        )
        except Exception as e:
            logger.error(
                f"Error renewing webhook subscription {subscription_id}: {e}",
                exc_info=True,
            )
            raise Exception(
                f"Failed to renew webhook subscription {subscription_id}: {str(e)}"
            )

    async def delete_webhook_subscription(
        self, subscription_id: str, token: str
    ) -> bool:
        """Delete an existing webhook subscription."""
        if not subscription_id:
            raise ValueError("Subscription ID is required to delete.")

        subscription_url = (
            f"https://graph.microsoft.com/v1.0/subscriptions/{subscription_id}"
        )
        headers = {"Authorization": f"Bearer {token}"}

        logger.info(f"Attempting to delete webhook subscription {subscription_id}")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.delete(
                    subscription_url, headers=headers
                ) as response:
                    # Successful deletion returns 204 No Content
                    if response.status == 204:
                        logger.info(
                            f"Successfully deleted webhook subscription {subscription_id}"
                        )
                        return True
                    else:
                        error_text = await response.text()
                        response_data = {}
                        try:
                            response_data = await response.json()
                        except:
                            pass
                        logger.error(
                            f"Failed to delete webhook subscription {subscription_id}: {response.status} - {error_text}"
                        )
                        logger.error(f"Response body: {response_data}")
                        # Don't raise an exception here, just return False maybe?
                        # Or re-raise depending on desired behavior on failure.
                        # Let's re-raise for now to make failures explicit.
                        raise Exception(
                            f"API error deleting subscription {subscription_id}: {response.status} - {error_text} - Body: {response_data}"
                        )
        except Exception as e:
            logger.error(
                f"Error deleting webhook subscription {subscription_id}: {e}",
                exc_info=True,
            )
            raise Exception(
                f"Failed to delete webhook subscription {subscription_id}: {str(e)}"
            )

    async def get_item_by_path(
        self, drive_id: str, folder_path: str, token: str
    ) -> Dict[str, Any]:
        """Get a drive item by its path within the drive."""
        
        # AGGRESSIVE OVERRIDE: Always use correct drive ID regardless of input
        original_drive_id = drive_id
        correct_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
        
        # Force override for any suspicious drive ID
        if "qcVTa1bR3USFQ8afSRiasrisaj4jPX5Ai9iQ-8Zwf7xFoR2Lh1amSZHp_Twn8Jfe" in drive_id:
            drive_id = correct_drive_id
            logger.info(f"FORCED OVERRIDE in get_item_by_path: {original_drive_id} -> {correct_drive_id}")
        
        # Log every call to see what's happening
        logger.info(f"get_item_by_path called with drive_id: {drive_id}, folder_path: {folder_path}")
        
        if not drive_id:
            raise ValueError("Drive ID is required.")
        if not folder_path:
            raise ValueError("Folder path is required.")

        try:
            # URL encode the folder path and handle special characters
            safe_path = folder_path.replace("'", "''")  # Handle single quotes
            encoded_path = quote(safe_path)
            
            # Construct the API URL to get item by path
            url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{encoded_path}"
            headers = {
                "Authorization": f"Bearer {token}",
                "Accept": "application/json",
            }
            
            logger.info(f"Getting item by path: {folder_path} from drive {drive_id}")
            logger.info(f"Safe path: {safe_path}")
            logger.info(f"Encoded path: {encoded_path}")
            logger.info(f"Using API URL: {url}")

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        item_data = await response.json()
                        logger.info(
                            f"Successfully retrieved item at path '{folder_path}' with ID: {item_data.get('id')}"
                        )
                        return item_data
                    else:
                        error_text = await response.text()
                        logger.error(
                            f"Failed to get item by path '{folder_path}': {response.status} - {error_text}"
                        )
                        
                        if response.status == 404:
                            raise Exception(
                                f"Path '{folder_path}' not found in drive {drive_id}"
                            )
                        elif response.status == 401:
                            raise Exception(
                                "Authentication error. Token may be invalid or expired."
                            )
                        elif response.status == 403:
                            raise Exception(
                                f"Permission denied accessing path '{folder_path}'"
                            )
                        else:
                            raise Exception(
                                f"API error getting item by path: {response.status} - {error_text}"
                            )

        except Exception as e:
            logger.error(f"Error getting item by path '{folder_path}': {e}", exc_info=True)
            raise Exception(f"Failed to get item by path '{folder_path}': {str(e)}")


# Example usage (for testing purposes, typically called from app logic)
# async def main():
#     # Assuming you have a valid token and drive_id
#     token = "YOUR_VALID_ACCESS_TOKEN"
#     drive_id = "YOUR_DRIVE_ID"
#     notification_url = "YOUR_PUBLIC_NGROK_OR_DEPLOYED_URL/webhook/sharepoint"
#
#     client = SharePointClient()
#     try:
#         subscription = await client.create_webhook_subscription(drive_id, notification_url, token)
#         print("Subscription created:", subscription)
#     except Exception as e:
#         print(f"Error: {e}")
#
# if __name__ == "__main__":
#     import asyncio
#     # Ensure you have a way to provide the token and drive_id
#     # asyncio.run(main())
