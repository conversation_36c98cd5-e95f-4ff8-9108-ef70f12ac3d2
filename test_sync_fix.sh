#!/bin/bash

# Test script for SharePoint Sync Deletion Fix
# Usage: ./test_sync_fix.sh [MS_TOKEN]

BASE_URL="http://localhost:8082"
MS_TOKEN="$1"

if [ -z "$MS_TOKEN" ]; then
    echo "Usage: $0 <MS_TOKEN>"
    echo "Please provide your Microsoft authentication token"
    exit 1
fi

echo "🧪 Testing SharePoint Sync Deletion Fix"
echo "========================================"

# Test 1: Health Check
echo "📊 Test 1: Checking sync health..."
curl -s -X GET "$BASE_URL/api/sync/health-dashboard" \
     -H "Authorization: Bearer $MS_TOKEN" | jq '.sync_health'

# Test 2: Check for orphaned documents
echo "🧹 Test 2: Checking for orphaned documents..."
curl -s -X POST "$BASE_URL/api/documents/cleanup-orphaned" | jq '.orphaned_documents_found'

# Test 3: Verify sync status
echo "🔍 Test 3: Verifying sync status..."
curl -s -X GET "$BASE_URL/api/sync/verify" \
     -H "Authorization: Bearer $MS_TOKEN" | jq '.sync_status'

# Test 4: Check background sync
echo "⏰ Test 4: Checking background sync status..."
curl -s -X GET "$BASE_URL/api/sync/background-status" | jq '.sync_info'

# Test 5: Document count
echo "📄 Test 5: Current document count..."
DOC_COUNT=$(curl -s -X GET "$BASE_URL/api/documents" | jq '.total_documents')
echo "Total documents in index: $DOC_COUNT"

echo ""
echo "✅ Basic tests completed!"
echo "💡 For full testing, follow the manual steps in the testing plan"
echo "🔗 Access health dashboard: $BASE_URL/api/sync/health-dashboard"
