#!/bin/bash

# Test and Fix SharePoint Sync Path Issue
# This script helps verify the SharePoint sync is pointing to the correct folder

echo "=========================================="
echo "SharePoint Sync Path Verification Script"
echo "=========================================="
echo "Date: $(date)"
echo ""

# Check if app is running
check_app_running() {
    if lsof -i :8082 | grep -q LISTEN; then
        echo "✅ App is running on port 8082"
        return 0
    else
        echo "❌ App is not running on port 8082"
        return 1
    fi
}

# Show current configuration
echo "📋 Current Configuration:"
echo "-------------------------"

# Check .env file
if [ -f .env ]; then
    echo "SYNC_TARGET_FOLDER_PATH: $(grep SYNC_TARGET_FOLDER_PATH .env | cut -d'=' -f2 || echo 'Not set')"
    echo "SharePoint Site: $(grep SHAREPOINT_SITE_NAME .env | cut -d'=' -f2)"
else
    echo "❌ .env file not found"
fi

# Check current index status
echo ""
echo "📊 Current Index Status:"
if [ -f storage/docstore.json ]; then
    python3 -c "
import json
with open('storage/docstore.json', 'r') as f:
    data = json.load(f)
    docs = data.get('docstore/data', {})
    print(f'Total indexed documents: {len(docs)}')
    sp_count = sum(1 for d in docs.values() if d.get('__data__',{}).get('metadata',{}).get('sharepoint_id'))
    print(f'SharePoint documents: {sp_count}')
    print(f'Local documents: {len(docs) - sp_count}')
"
else
    echo "No index found (storage/docstore.json missing)"
fi

echo ""
echo "=========================================="
echo "Available Actions:"
echo "=========================================="
echo "1. Run verify_sharepoint_path.py to check folder contents"
echo "2. Run clear_index.py to clear the index for a fresh sync"
echo "3. Start the app with: uvicorn app:app --host localhost --port 8082"
echo ""

if check_app_running; then
    echo "App is running. You can:"
    echo "- Login as admin"
    echo "- Go to SharePoint sync"
    echo "- Check the logs for sync path diagnostics"
else
    echo "App is not running. Start it to perform sync operations."
fi

echo ""
echo "=========================================="
echo "Summary of Issue:"
echo "=========================================="
echo "The app was syncing 75 documents, but the correct"
echo "DDB Group Repository only has 2 empty subfolders:"
echo "- Human Resources"
echo "- Culture Hub"
echo ""
echo "This means the 75 documents are likely from:"
echo "1. An earlier sync when the folders had content"
echo "2. OR the app was syncing from a different location"
echo ""
echo "The sync path is now configurable via SYNC_TARGET_FOLDER_PATH"
echo "=========================================="
